// 简化的GPU挖矿着色器
// 专注于高性能并行计算

// 输入数据结构
struct HashInput {
    nonce_start_low: u32,
    nonce_start_high: u32,
    target_zeros: u32,
    padding1: u32,
}

// 输出结果结构
struct HashResult {
    found_nonce_low: u32,
    found_nonce_high: u32,
    hash_prefix: array<u32, 8>,
    found: atomic<u32>,
    attempts: atomic<u32>,
}

// 绑定组
@group(0) @binding(0) var<storage, read> input: HashInput;
@group(0) @binding(1) var<storage, read_write> output: HashResult;

// 简化的哈希函数 (基于SHA-like算法)
fn compute_hash(nonce: u32) -> array<u32, 8> {
    var hash: array<u32, 8>;
    
    // 使用多轮变换来模拟加密哈希
    var state: array<u32, 8> = array<u32, 8>(
        0x6a09e667u, 0xbb67ae85u, 0x3c6ef372u, 0xa54ff53au,
        0x510e527fu, 0x9b05688cu, 0x1f83d9abu, 0x5be0cd19u
    );
    
    // 混入nonce
    state[0] ^= nonce;
    state[1] ^= nonce >> 8u;
    state[2] ^= nonce >> 16u;
    state[3] ^= nonce >> 24u;
    
    // 多轮混合
    for (var round: u32 = 0u; round < 16u; round++) {
        for (var i: u32 = 0u; i < 8u; i++) {
            let j = (i + 1u) % 8u;
            let k = (i + 2u) % 8u;
            state[i] = state[i] ^ ((state[j] << 7u) | (state[j] >> 25u)) ^ state[k];
        }
        
        // 添加轮常量
        state[0] ^= round * 0x9e3779b9u;
    }
    
    // 最终混合
    for (var i: u32 = 0u; i < 8u; i++) {
        hash[i] = state[i] ^ (state[i] >> 16u);
    }
    
    return hash;
}

// 检查哈希是否符合目标零的数量
fn check_target(hash: array<u32, 8>, target_zeros: u32) -> bool {
    // 检查前导零的数量（以十六进制字符计）
    let full_words = target_zeros / 8u;
    let remaining_nibbles = target_zeros % 8u;
    
    // 检查完整的零字
    for (var i: u32 = 0u; i < full_words; i++) {
        if (hash[i] != 0u) {
            return false;
        }
    }
    
    // 检查剩余的半字节
    if (remaining_nibbles > 0u && full_words < 8u) {
        let word = hash[full_words];
        let shift = (8u - remaining_nibbles) * 4u;
        if ((word >> shift) != 0u) {
            return false;
        }
    }
    
    return true;
}

// 主计算函数
@compute @workgroup_size(256)
fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
    let thread_id = global_id.x;
    let nonce = input.nonce_start_low + thread_id;
    
    // 计算哈希
    let hash = compute_hash(nonce);
    
    // 检查是否符合目标
    if (check_target(hash, input.target_zeros)) {
        // 使用原子操作确保只有一个线程写入结果
        let old_found = atomicLoad(&output.found);
        if (old_found == 0u) {
            if (atomicCompareExchangeWeak(&output.found, 0u, 1u).exchanged) {
                output.found_nonce_low = nonce;
                output.found_nonce_high = input.nonce_start_high;
                output.hash_prefix = hash;
                atomicStore(&output.attempts, thread_id + 1u);
            }
        }
    }
}
